import * as cdk from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as certificatemanager from 'aws-cdk-lib/aws-certificatemanager';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as nodejs from 'aws-cdk-lib/aws-lambda-nodejs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { allRequestValidatorModels, methodDocumentation } from './api-schemas';
import {
  createPostMethodOptions,
  createGetMethodOptions,
  createAuthorizedMethodOptions,
  AUTHORIZATION_REQUEST_PARAMETERS,
  HTTP_STATUS_CODES
} from './validation-utils';

export interface GameFlexBackendStackProps extends cdk.StackProps {
  environment: string;
  projectName: string;
  domainName?: string;
  certificateArn?: string;
  r2BucketName: string;
  r2PublicUrl: string;
}

export class GameFlexBackendStack extends cdk.Stack {
  public readonly userPool: cognito.UserPool;
  public readonly userPoolClient: cognito.UserPoolClient;
  public readonly api: apigateway.RestApi;
  public readonly tables: { [key: string]: dynamodb.Table };

  constructor(scope: Construct, id: string, props: GameFlexBackendStackProps) {
    super(scope, id, props);

    const { environment, projectName, domainName, certificateArn, r2BucketName, r2PublicUrl } = props;

    // Conditions
    const isProduction = environment === 'production';
    const isStaging = environment === 'staging';
    const isProductionOrStaging = isProduction || isStaging;
    const hasCustomDomain = !!domainName;

    // AWS Secrets Manager for R2 Configuration
    // Use fromSecretNameV2 to reference existing secrets or create new ones
    const r2SecretName = `${projectName}-r2-config-${environment}`;
    const r2Secret = secretsmanager.Secret.fromSecretNameV2(this, 'R2Secret', r2SecretName);

    // AWS Secrets Manager for General Configuration
    const appConfigSecretName = `${projectName}-app-config-${environment}`;
    const appConfigSecret = secretsmanager.Secret.fromSecretNameV2(this, 'AppConfigSecret', appConfigSecretName);

    // AWS Secrets Manager for Apple Sign In Configuration
    const appleConfigSecretName = `${projectName}-apple-config-${environment}`;
    const appleConfigSecret = secretsmanager.Secret.fromSecretNameV2(this, 'AppleConfigSecret', appleConfigSecretName);

    // Note: Cannot apply removal policies to imported secrets
    // Secrets are managed externally and will persist regardless

    // Cognito User Pool
    this.userPool = new cognito.UserPool(this, 'UserPool', {
      userPoolName: `${projectName}-users-${environment}`,
      autoVerify: { email: true },
      signInAliases: { email: true },
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireDigits: true,
        requireSymbols: false,
      },
      deletionProtection: isProductionOrStaging,
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    });

    // Cognito User Pool Client
    this.userPoolClient = new cognito.UserPoolClient(this, 'UserPoolClient', {
      userPool: this.userPool,
      userPoolClientName: `${projectName}-client-${environment}`,
      generateSecret: false,
      authFlows: {
        adminUserPassword: true,
        userPassword: true,
        userSrp: true,
      },
      refreshTokenValidity: cdk.Duration.days(30),
      accessTokenValidity: cdk.Duration.minutes(60),
      idTokenValidity: cdk.Duration.minutes(60),
    });

    // DynamoDB Tables
    this.tables = this.createDynamoDBTables(projectName, environment, isProductionOrStaging);

    // Lambda Functions
    const lambdaFunctions = this.createLambdaFunctions(
      projectName,
      environment,
      this.userPool,
      this.userPoolClient,
      this.tables,
      r2Secret,
      appConfigSecret,
      appleConfigSecret
    );

    // API Gateway
    this.api = this.createApiGateway(projectName, environment, lambdaFunctions, this.userPool);

    // Custom Domain (if specified)
    if (hasCustomDomain && certificateArn) {
      this.createOrAttachCustomDomain(domainName!, certificateArn, environment);
    }

    // Outputs
    this.createOutputs(environment, domainName, r2Secret, appConfigSecret, appleConfigSecret);
  }

  private createDynamoDBTables(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean
  ): { [key: string]: dynamodb.Table } {
    const tableConfig = {
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      deletionProtection: isProductionOrStaging,
      pointInTimeRecoverySpecification: {
        pointInTimeRecoveryEnabled: isProductionOrStaging,
      },
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    };

    const tables: { [key: string]: dynamodb.Table } = {};

    // Posts Table
    tables.posts = new dynamodb.Table(this, 'PostsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Posts`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });

    // Media Table
    tables.media = new dynamodb.Table(this, 'MediaTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Media`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });

    // User Profiles Table
    tables.userProfiles = new dynamodb.Table(this, 'UserProfilesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-UserProfiles`,
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Comments Table
    tables.comments = new dynamodb.Table(this, 'CommentsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Comments`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.comments.addGlobalSecondaryIndex({
      indexName: 'postId-index',
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
    });

    // Likes Table
    tables.likes = new dynamodb.Table(this, 'LikesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Likes`,
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.likes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Follows Table
    tables.follows = new dynamodb.Table(this, 'FollowsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Follows`,
      partitionKey: { name: 'followerId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'followingId', type: dynamodb.AttributeType.STRING },
    });

    // Channels Table
    tables.channels = new dynamodb.Table(this, 'ChannelsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Channels`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.channels.addGlobalSecondaryIndex({
      indexName: 'ownerId-index',
      partitionKey: { name: 'ownerId', type: dynamodb.AttributeType.STRING },
    });

    // Channel Members Table
    tables.channelMembers = new dynamodb.Table(this, 'ChannelMembersTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-ChannelMembers`,
      partitionKey: { name: 'channelId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.channelMembers.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Reflexes Table
    tables.reflexes = new dynamodb.Table(this, 'ReflexesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Reflexes`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexes.addGlobalSecondaryIndex({
      indexName: 'postId-index',
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Reflex Likes Table
    tables.reflexLikes = new dynamodb.Table(this, 'ReflexLikesTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-ReflexLikes`,
      partitionKey: { name: 'reflexId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });
    tables.reflexLikes.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });


    // Post Reactions Table
    tables.postReactions = new dynamodb.Table(this, 'PostReactionsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-PostReactions`,
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'reactionKey', type: dynamodb.AttributeType.STRING }, // format: "emoji#userId"
    });
    tables.postReactions.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Reflex Reactions Table
    tables.reflexReactions = new dynamodb.Table(this, 'ReflexReactionsTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-ReflexReactions`,
      partitionKey: { name: 'reflexId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'reactionKey', type: dynamodb.AttributeType.STRING }, // format: "emoji#userId"
    });
    tables.reflexReactions.addGlobalSecondaryIndex({
      indexName: 'userId-index',
      partitionKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
    });

    // Users Table
    tables.users = new dynamodb.Table(this, 'UsersTable', {
      ...tableConfig,
      tableName: `${projectName}-${environment}-Users`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'EmailIndex',
      partitionKey: { name: 'email', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'UsernameIndex',
      partitionKey: { name: 'username', type: dynamodb.AttributeType.STRING },
    });
    tables.users.addGlobalSecondaryIndex({
      indexName: 'CognitoUserIdIndex',
      partitionKey: { name: 'cognitoUserId', type: dynamodb.AttributeType.STRING },
    });

    return tables;
  }

  private createLambdaFunctions(
    projectName: string,
    environment: string,
    userPool: cognito.UserPool,
    userPoolClient: cognito.UserPoolClient,
    tables: { [key: string]: dynamodb.Table },
    r2Secret: secretsmanager.ISecret,
    appConfigSecret: secretsmanager.ISecret,
    appleConfigSecret: secretsmanager.ISecret
  ): { [key: string]: lambda.Function } {
    const functions: { [key: string]: lambda.Function } = {};

    // Common environment variables for all Lambda functions
    const commonEnvironment = {
      ENVIRONMENT: environment,
      PROJECT_NAME: projectName,
      USER_POOL_ID: userPool.userPoolId,
      USER_POOL_CLIENT_ID: userPoolClient.userPoolClientId,
      POSTS_TABLE: tables.posts.tableName,
      MEDIA_TABLE: tables.media.tableName,
      USER_PROFILES_TABLE: tables.userProfiles.tableName,
      COMMENTS_TABLE: tables.comments.tableName,
      LIKES_TABLE: tables.likes.tableName,
      FOLLOWS_TABLE: tables.follows.tableName,
      CHANNELS_TABLE: tables.channels.tableName,
      CHANNEL_MEMBERS_TABLE: tables.channelMembers.tableName,
      REFLEXES_TABLE: tables.reflexes.tableName,
      REFLEX_LIKES_TABLE: tables.reflexLikes.tableName,
      POST_REACTIONS_TABLE: tables.postReactions.tableName,
      REFLEX_REACTIONS_TABLE: tables.reflexReactions.tableName,
      USERS_TABLE: tables.users.tableName,
      R2_SECRET_NAME: r2Secret.secretName,
      APP_CONFIG_SECRET_NAME: appConfigSecret.secretName,
      APPLE_CONFIG_SECRET_NAME: appleConfigSecret.secretName,
    };

    // Authorizer Function
    functions.authorizer = new lambda.Function(this, 'AuthorizerFunction', {
      functionName: `${projectName}-authorizer-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/authorizer'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: {
        ...commonEnvironment,
        USER_POOL_ID: userPool.userPoolId,
      },
    });

    // Grant permissions to authorizer
    userPool.grant(functions.authorizer, 'cognito-idp:GetUser');

    // Auth Functions - separate functions for each operation
    functions.authSignup = new lambda.Function(this, 'AuthSignupFunction', {
      functionName: `${projectName}-auth-signup-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'signup.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authSignin = new lambda.Function(this, 'AuthSigninFunction', {
      functionName: `${projectName}-auth-signin-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'signin.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authRefresh = new lambda.Function(this, 'AuthRefreshFunction', {
      functionName: `${projectName}-auth-refresh-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'refresh.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authValidate = new lambda.Function(this, 'AuthValidateFunction', {
      functionName: `${projectName}-auth-validate-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'validate.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    functions.authSetUsername = new lambda.Function(this, 'AuthSetUsernameFunction', {
      functionName: `${projectName}-auth-set-username-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'set-username.handler',
      code: lambda.Code.fromAsset('src/auth'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to auth functions
    tables.users.grantReadWriteData(functions.authSignup);
    userPool.grant(functions.authSignup, 'cognito-idp:*');

    tables.users.grantReadData(functions.authSignin);
    userPool.grant(functions.authSignin, 'cognito-idp:AdminInitiateAuth');
    appleConfigSecret.grantRead(functions.authSignin);

    userPool.grant(functions.authRefresh, 'cognito-idp:AdminInitiateAuth');

    tables.users.grantReadData(functions.authValidate);
    userPool.grant(functions.authValidate, 'cognito-idp:GetUser');

    tables.users.grantReadWriteData(functions.authSetUsername);

    // Posts Function
    functions.posts = new lambda.Function(this, 'PostsFunction', {
      functionName: `${projectName}-posts-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/posts'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to posts function
    tables.posts.grantReadWriteData(functions.posts);
    tables.media.grantReadWriteData(functions.posts);
    tables.comments.grantReadWriteData(functions.posts);
    tables.likes.grantReadWriteData(functions.posts);
    tables.reflexes.grantReadWriteData(functions.posts);
    tables.users.grantReadWriteData(functions.posts);
    tables.postReactions.grantReadWriteData(functions.posts);
    tables.reflexReactions.grantReadWriteData(functions.posts);


    // Media Function
    functions.media = new lambda.Function(this, 'MediaFunction', {
      functionName: `${projectName}-media-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/media'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to media function
    tables.media.grantReadWriteData(functions.media);
    r2Secret.grantRead(functions.media);
    appConfigSecret.grantRead(functions.media);

    // Users Function
    functions.users = new lambda.Function(this, 'UsersFunction', {
      functionName: `${projectName}-users-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/users'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to users function
    tables.users.grantReadWriteData(functions.users);
    tables.userProfiles.grantReadWriteData(functions.users);
    tables.follows.grantReadWriteData(functions.users);
    tables.posts.grantReadWriteData(functions.users);
    tables.likes.grantReadWriteData(functions.users);

    // Health Function
    functions.health = new lambda.Function(this, 'HealthFunction', {
      functionName: `${projectName}-health-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/health'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to health function
    tables.users.grantReadData(functions.health);

    // Reflexes Function
    functions.reflexes = new lambda.Function(this, 'ReflexesFunction', {
      functionName: `${projectName}-reflexes-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/reflexes'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to reflexes function
    tables.reflexes.grantReadWriteData(functions.reflexes);
    tables.reflexLikes.grantReadWriteData(functions.reflexes);
    tables.posts.grantReadWriteData(functions.reflexes);
    tables.users.grantReadWriteData(functions.reflexes);
    tables.media.grantReadWriteData(functions.reflexes);
    tables.postReactions.grantReadWriteData(functions.reflexes);
    tables.reflexReactions.grantReadWriteData(functions.reflexes);


    // Channels Function
    functions.channels = new lambda.Function(this, 'ChannelsFunction', {
      functionName: `${projectName}-channels-${environment}`,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/channels'),
      timeout: cdk.Duration.seconds(30),
      memorySize: 256,
      environment: commonEnvironment,
    });

    // Grant permissions to channels function
    tables.channels.grantReadWriteData(functions.channels);
    tables.channelMembers.grantReadWriteData(functions.channels);
    tables.users.grantReadWriteData(functions.channels);
    tables.posts.grantReadWriteData(functions.channels);
    tables.media.grantReadWriteData(functions.channels);
    r2Secret.grantRead(functions.channels);
    appConfigSecret.grantRead(functions.channels);

    return functions;
  }

  private createApiGateway(
    projectName: string,
    environment: string,
    functions: { [key: string]: lambda.Function },
    userPool: cognito.UserPool
  ): apigateway.RestApi {
    // Create API Gateway
    const api = new apigateway.RestApi(this, 'GameFlexApi', {
      restApiName: `${projectName}-api-${environment}`,
      description: `GameFlex API for ${environment} environment`,
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [
          'Content-Type',
          'X-Amz-Date',
          'Authorization',
          'X-Api-Key',
          'X-Amz-Security-Token',
          'X-Amz-User-Agent',
        ],
      },
      deployOptions: {
        stageName: 'v1',
        throttlingRateLimit: 1000,
        throttlingBurstLimit: 2000,
        // Enable documentation generation
        documentationVersion: '1.0.0',
        description: 'GameFlex API v1.0.0 with comprehensive authentication endpoints',
      },
      // Enable API Gateway documentation
      apiKeySourceType: apigateway.ApiKeySourceType.HEADER,
      endpointConfiguration: {
        types: [apigateway.EndpointType.REGIONAL]
      },
    });

    // Create authorizer with environment-specific name
    const authorizer = new apigateway.TokenAuthorizer(this, 'DefaultAuthorizer', {
      handler: functions.authorizer,
      authorizerName: `DefaultAuthorizer${environment}`,
      resultsCacheTtl: cdk.Duration.minutes(5),
    });

    // Create Request Validators with environment-specific names
    const bodyValidator = new apigateway.RequestValidator(this, 'BodyValidator', {
      restApi: api,
      requestValidatorName: `bodyValidator${environment}`,
      validateRequestBody: true,
      validateRequestParameters: false,
    });

    const parameterValidator = new apigateway.RequestValidator(this, 'ParameterValidator', {
      restApi: api,
      requestValidatorName: `parameterValidator${environment}`,
      validateRequestBody: false,
      validateRequestParameters: true,
    });

    const bodyAndParameterValidator = new apigateway.RequestValidator(this, 'BodyAndParameterValidator', {
      restApi: api,
      requestValidatorName: `bodyAndParameterValidator${environment}`,
      validateRequestBody: true,
      validateRequestParameters: true,
    });

    // Create API Models for request validation with environment-specific names
    const signupRequestModel = new apigateway.Model(this, 'SignupRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.SignupRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.SignupRequest.contentType,
      schema: allRequestValidatorModels.SignupRequest.schema,
      description: allRequestValidatorModels.SignupRequest.description,
    });

    const signinRequestModel = new apigateway.Model(this, 'SigninRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.SigninRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.SigninRequest.contentType,
      schema: allRequestValidatorModels.SigninRequest.schema,
      description: allRequestValidatorModels.SigninRequest.description,
    });

    const refreshRequestModel = new apigateway.Model(this, 'RefreshRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.RefreshRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.RefreshRequest.contentType,
      schema: allRequestValidatorModels.RefreshRequest.schema,
      description: allRequestValidatorModels.RefreshRequest.description,
    });

    const appleSigninRequestModel = new apigateway.Model(this, 'AppleSigninRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.AppleSigninRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.AppleSigninRequest.contentType,
      schema: allRequestValidatorModels.AppleSigninRequest.schema,
      description: allRequestValidatorModels.AppleSigninRequest.description,
    });

    const setUsernameRequestModel = new apigateway.Model(this, 'SetUsernameRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.SetUsernameRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.SetUsernameRequest.contentType,
      schema: allRequestValidatorModels.SetUsernameRequest.schema,
      description: allRequestValidatorModels.SetUsernameRequest.description,
    });

    // Create response models with environment-specific names
    const signupResponseModel = new apigateway.Model(this, 'SignupResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.SignupResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.SignupResponse.contentType,
      schema: allRequestValidatorModels.SignupResponse.schema,
      description: allRequestValidatorModels.SignupResponse.description,
    });

    const signinResponseModel = new apigateway.Model(this, 'SigninResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.SigninResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.SigninResponse.contentType,
      schema: allRequestValidatorModels.SigninResponse.schema,
      description: allRequestValidatorModels.SigninResponse.description,
    });

    const refreshResponseModel = new apigateway.Model(this, 'RefreshResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.RefreshResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.RefreshResponse.contentType,
      schema: allRequestValidatorModels.RefreshResponse.schema,
      description: allRequestValidatorModels.RefreshResponse.description,
    });

    const validateResponseModel = new apigateway.Model(this, 'ValidateResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.ValidateResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.ValidateResponse.contentType,
      schema: allRequestValidatorModels.ValidateResponse.schema,
      description: allRequestValidatorModels.ValidateResponse.description,
    });

    const setUsernameResponseModel = new apigateway.Model(this, 'SetUsernameResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.SetUsernameResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.SetUsernameResponse.contentType,
      schema: allRequestValidatorModels.SetUsernameResponse.schema,
      description: allRequestValidatorModels.SetUsernameResponse.description,
    });

    const errorResponseModel = new apigateway.Model(this, 'ErrorResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.ErrorResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.ErrorResponse.contentType,
      schema: allRequestValidatorModels.ErrorResponse.schema,
      description: allRequestValidatorModels.ErrorResponse.description,
    });

    // Posts API Models with environment-specific names
    const createPostRequestModel = new apigateway.Model(this, 'CreatePostRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.CreatePostRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.CreatePostRequest.contentType,
      schema: allRequestValidatorModels.CreatePostRequest.schema,
      description: allRequestValidatorModels.CreatePostRequest.description,
    });

    const updatePostRequestModel = new apigateway.Model(this, 'UpdatePostRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.UpdatePostRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.UpdatePostRequest.contentType,
      schema: allRequestValidatorModels.UpdatePostRequest.schema,
      description: allRequestValidatorModels.UpdatePostRequest.description,
    });

    const attachMediaRequestModel = new apigateway.Model(this, 'AttachMediaRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.AttachMediaRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.AttachMediaRequest.contentType,
      schema: allRequestValidatorModels.AttachMediaRequest.schema,
      description: allRequestValidatorModels.AttachMediaRequest.description,
    });

    const postResponseModel = new apigateway.Model(this, 'PostResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.PostResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.PostResponse.contentType,
      schema: allRequestValidatorModels.PostResponse.schema,
      description: allRequestValidatorModels.PostResponse.description,
    });

    const postsListResponseModel = new apigateway.Model(this, 'PostsListResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.PostsListResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.PostsListResponse.contentType,
      schema: allRequestValidatorModels.PostsListResponse.schema,
      description: allRequestValidatorModels.PostsListResponse.description,
    });

    // Users API Models with environment-specific names
    const updateProfileRequestModel = new apigateway.Model(this, 'UpdateProfileRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.UpdateProfileRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.UpdateProfileRequest.contentType,
      schema: allRequestValidatorModels.UpdateProfileRequest.schema,
      description: allRequestValidatorModels.UpdateProfileRequest.description,
    });

    const userProfileResponseModel = new apigateway.Model(this, 'UserProfileResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.UserProfileResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.UserProfileResponse.contentType,
      schema: allRequestValidatorModels.UserProfileResponse.schema,
      description: allRequestValidatorModels.UserProfileResponse.description,
    });

    const followResponseModel = new apigateway.Model(this, 'FollowResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.FollowResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.FollowResponse.contentType,
      schema: allRequestValidatorModels.FollowResponse.schema,
      description: allRequestValidatorModels.FollowResponse.description,
    });

    // Media API Models with environment-specific names
    const uploadMediaRequestModel = new apigateway.Model(this, 'UploadMediaRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.UploadMediaRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.UploadMediaRequest.contentType,
      schema: allRequestValidatorModels.UploadMediaRequest.schema,
      description: allRequestValidatorModels.UploadMediaRequest.description,
    });

    const uploadMediaResponseModel = new apigateway.Model(this, 'UploadMediaResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.UploadMediaResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.UploadMediaResponse.contentType,
      schema: allRequestValidatorModels.UploadMediaResponse.schema,
      description: allRequestValidatorModels.UploadMediaResponse.description,
    });

    const mediaResponseModel = new apigateway.Model(this, 'MediaResponseModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.MediaResponse.modelName}${environment}`,
      contentType: allRequestValidatorModels.MediaResponse.contentType,
      schema: allRequestValidatorModels.MediaResponse.schema,
      description: allRequestValidatorModels.MediaResponse.description,
    });

    const updateMediaStatusRequestModel = new apigateway.Model(this, 'UpdateMediaStatusRequestModel', {
      restApi: api,
      modelName: `${allRequestValidatorModels.UpdateMediaStatusRequest.modelName}${environment}`,
      contentType: allRequestValidatorModels.UpdateMediaStatusRequest.contentType,
      schema: allRequestValidatorModels.UpdateMediaStatusRequest.schema,
      description: allRequestValidatorModels.UpdateMediaStatusRequest.description,
    });

    // Create API resources and methods directly under root
    // Since the stage is already 'v1', we don't need a nested v1 resource

    // Health endpoint (no auth required)
    const health = api.root.addResource('health');
    health.addMethod('GET', new apigateway.LambdaIntegration(functions.health));

    // Auth endpoints (no auth required) - proper REST endpoints with validation
    const auth = api.root.addResource('auth');

    // POST /auth/signup - Create new user account
    const authSignup = auth.addResource('signup');
    authSignup.addMethod('POST', new apigateway.LambdaIntegration(functions.authSignup),
      createPostMethodOptions(
        bodyValidator,
        signupRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.CREATED,
        signupResponseModel
      )
    );

    // POST /auth/signin - Authenticate user and get tokens
    const authSignin = auth.addResource('signin');
    authSignin.addMethod('POST', new apigateway.LambdaIntegration(functions.authSignin),
      createPostMethodOptions(
        bodyValidator,
        signinRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        signinResponseModel
      )
    );

    // POST /auth/refresh - Refresh access token using refresh token
    const authRefresh = auth.addResource('refresh');
    authRefresh.addMethod('POST', new apigateway.LambdaIntegration(functions.authRefresh),
      createPostMethodOptions(
        bodyValidator,
        refreshRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        refreshResponseModel
      )
    );

    // GET /auth/validate - Validate access token and get user info
    const authValidate = auth.addResource('validate');
    authValidate.addMethod('GET', new apigateway.LambdaIntegration(functions.authValidate),
      createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        validateResponseModel
      )
    );

    // POST /auth/apple - Sign in with Apple
    const authApple = auth.addResource('apple');
    authApple.addMethod('POST', new apigateway.LambdaIntegration(functions.authSignin),
      createPostMethodOptions(
        bodyValidator,
        appleSigninRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        signinResponseModel
      )
    );

    // POST /auth/set-username - Set username for authenticated user
    const authSetUsername = auth.addResource('set-username');
    authSetUsername.addMethod('POST', new apigateway.LambdaIntegration(functions.authSetUsername), {
      ...createPostMethodOptions(
        bodyValidator,
        setUsernameRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        setUsernameResponseModel
      ),
      authorizer,
    });

    // Posts endpoints (auth required)
    const posts = api.root.addResource('posts');

    // GET /posts - Get all posts
    posts.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        postsListResponseModel
      ),
      authorizer,
    });

    // POST /posts - Create new post
    posts.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      ...createPostMethodOptions(
        bodyValidator,
        createPostRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.CREATED,
        postResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS
      ),
      authorizer,
    });

    // POST /posts/draft - Create draft post
    const postsDraft = posts.addResource('draft');
    postsDraft.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      ...createPostMethodOptions(
        bodyValidator,
        createPostRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.CREATED,
        postResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS
      ),
      authorizer,
    });

    // Individual post endpoints
    const postId = posts.addResource('{id}');

    // GET /posts/{id} - Get post by ID
    postId.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        postResponseModel
      ),
      authorizer,
    });

    // PUT /posts/{id} - Update post
    postId.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
      ...createPostMethodOptions(
        bodyValidator,
        updatePostRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        postResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS
      ),
      authorizer,
    });

    // DELETE /posts/{id} - Delete post
    postId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // PUT /posts/{id}/media - Attach media to post
    const postMedia = postId.addResource('media');
    postMedia.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
      ...createPostMethodOptions(
        bodyValidator,
        attachMediaRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        postResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS
      ),
      authorizer,
    });

    // PUT /posts/{id}/publish - Publish post
    const postPublish = postId.addResource('publish');
    postPublish.addMethod('PUT', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        postResponseModel
      ),
      authorizer,
    });

    // Like endpoints
    const postLike = postId.addResource('like');

    // POST /posts/{id}/like - Like post
    postLike.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // DELETE /posts/{id}/like - Unlike post
    postLike.addMethod('DELETE', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // Reactions endpoints
    const postReactions = postId.addResource('reactions');

    // POST /posts/{id}/reactions - Add reaction (body: { emoji })
    postReactions.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      ...createAuthorizedMethodOptions(authorizer, bodyValidator, undefined as any, errorResponseModel, AUTHORIZATION_REQUEST_PARAMETERS),
    });

    // DELETE /posts/{id}/reactions?emoji= - Remove reaction
    postReactions.addMethod('DELETE', new apigateway.LambdaIntegration(functions.posts), {
      ...createAuthorizedMethodOptions(authorizer, parameterValidator, undefined as any, errorResponseModel, AUTHORIZATION_REQUEST_PARAMETERS),
    });

    // Comments endpoints
    const postComments = postId.addResource('comments');

    // GET /posts/{id}/comments - Get post comments
    postComments.addMethod('GET', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // POST /posts/{id}/comments - Add comment to post
    postComments.addMethod('POST', new apigateway.LambdaIntegration(functions.posts), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // Reflexes endpoints for posts
    const postReflexes = postId.addResource('reflexes');

    // GET /posts/{id}/reflexes - Get reflexes for a post
    postReflexes.addMethod('GET', new apigateway.LambdaIntegration(functions.reflexes), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // POST /posts/{id}/reflexes - Create reflex for a post
    postReflexes.addMethod('POST', new apigateway.LambdaIntegration(functions.reflexes), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // Users endpoints (auth required)
    const users = api.root.addResource('users');

    // User profile endpoints
    const profile = users.addResource('profile');

    // GET /users/profile - Get current user profile
    profile.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        userProfileResponseModel
      ),
      authorizer,
    });

    // PUT /users/profile - Update user profile
    profile.addMethod('PUT', new apigateway.LambdaIntegration(functions.users), {
      ...createPostMethodOptions(
        bodyValidator,
        updateProfileRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        userProfileResponseModel
      ),
      authorizer,
    });

    // Individual user endpoints
    const userId = users.addResource('{id}');

    // GET /users/{id} - Get user by ID
    userId.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        userProfileResponseModel
      ),
      authorizer,
    });

    // GET /users/profile/liked-posts - Get user liked posts
    const likedPosts = profile.addResource('liked-posts');
    likedPosts.addMethod('GET', new apigateway.LambdaIntegration(functions.users), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        postsListResponseModel
      ),
      authorizer,
    });

    // User follow endpoints
    const userFollow = userId.addResource('follow');

    // POST /users/{id}/follow - Follow user
    userFollow.addMethod('POST', new apigateway.LambdaIntegration(functions.users), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        followResponseModel
      ),
      authorizer,
    });

    // DELETE /users/{id}/follow - Unfollow user
    userFollow.addMethod('DELETE', new apigateway.LambdaIntegration(functions.users), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        followResponseModel
      ),
      authorizer,
    });

    // Media endpoints (auth required)
    const media = api.root.addResource('media');

    // POST /media/upload - Upload media
    const mediaUpload = media.addResource('upload');
    mediaUpload.addMethod('POST', new apigateway.LambdaIntegration(functions.media), {
      ...createPostMethodOptions(
        bodyValidator,
        uploadMediaRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.CREATED,
        uploadMediaResponseModel
      ),
      authorizer,
    });

    // Individual media endpoints
    const mediaId = media.addResource('{id}');

    // GET /media/{id} - Get media by ID
    mediaId.addMethod('GET', new apigateway.LambdaIntegration(functions.media), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true,
        mediaResponseModel
      ),
      authorizer,
    });

    // PUT /media/{id} - Update media status
    mediaId.addMethod('PUT', new apigateway.LambdaIntegration(functions.media), {
      ...createPostMethodOptions(
        bodyValidator,
        updateMediaStatusRequestModel,
        errorResponseModel,
        HTTP_STATUS_CODES.OK,
        mediaResponseModel
      ),
      authorizer,
    });

    // DELETE /media/{id} - Delete media
    mediaId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.media), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // Reflexes endpoints (auth required)
    const reflexes = api.root.addResource('reflexes');
    reflexes.addMethod('GET', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    reflexes.addMethod('POST', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    const reflexId = reflexes.addResource('{id}');
    reflexId.addMethod('GET', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    reflexId.addMethod('PUT', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });
    reflexId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.reflexes), {
      authorizer,
    });

    // Reflex like endpoints
    const reflexReactions2 = reflexId.addResource('reactions');
    reflexReactions2.addMethod('POST', new apigateway.LambdaIntegration(functions.reflexes), {
      ...createAuthorizedMethodOptions(authorizer, bodyValidator, undefined as any, errorResponseModel, AUTHORIZATION_REQUEST_PARAMETERS),
    });
    reflexReactions2.addMethod('DELETE', new apigateway.LambdaIntegration(functions.reflexes), {
      ...createAuthorizedMethodOptions(authorizer, parameterValidator, undefined as any, errorResponseModel, AUTHORIZATION_REQUEST_PARAMETERS),
    });

    const reflexLike = reflexId.addResource('like');

    // POST /reflexes/{id}/like - Like reflex
    reflexLike.addMethod('POST', new apigateway.LambdaIntegration(functions.reflexes), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // DELETE /reflexes/{id}/like - Unlike reflex
    reflexLike.addMethod('DELETE', new apigateway.LambdaIntegration(functions.reflexes), {
      ...createGetMethodOptions(
        parameterValidator,
        errorResponseModel,
        AUTHORIZATION_REQUEST_PARAMETERS,
        true
      ),
      authorizer,
    });

    // Channels endpoints (auth required)
    const channels = api.root.addResource('channels');
    channels.addMethod('GET', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    channels.addMethod('POST', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    const channelId = channels.addResource('{id}');
    channelId.addMethod('GET', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    channelId.addMethod('PUT', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });
    channelId.addMethod('DELETE', new apigateway.LambdaIntegration(functions.channels), {
      authorizer,
    });

    // Add API Documentation
    this.addApiDocumentation(api);

    return api;
  }

  private addApiDocumentation(api: apigateway.RestApi): void {
    // Create documentation parts for the API
    new apigateway.CfnDocumentationPart(this, 'ApiDocumentation', {
      restApiId: api.restApiId,
      location: {
        type: 'API'
      },
      properties: JSON.stringify({
        info: {
          title: 'GameFlex API',
          description: 'Comprehensive API for the GameFlex gaming platform with authentication, user management, posts, and media handling.',
          version: '1.0.0',
          contact: {
            name: 'GameFlex API Support',
            email: '<EMAIL>'
          }
        },
        tags: [
          {
            name: 'Authentication',
            description: 'User authentication and authorization endpoints'
          },
          {
            name: 'Users',
            description: 'User profile and management endpoints'
          },
          {
            name: 'Posts',
            description: 'Social media posts and content endpoints'
          },
          {
            name: 'Media',
            description: 'Media upload and management endpoints'
          }
        ]
      })
    });

    // Auth resource documentation
    new apigateway.CfnDocumentationPart(this, 'AuthResourceDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'RESOURCE',
        path: '/auth'
      },
      properties: JSON.stringify({
        description: 'Authentication endpoints for user registration, login, token refresh, and validation'
      })
    });

    // POST /auth/signup documentation
    new apigateway.CfnDocumentationPart(this, 'AuthSignupMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/auth/signup',
        method: 'POST'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.authSignup.summary,
        description: methodDocumentation.authSignup.description,
        tags: methodDocumentation.authSignup.tags,
        operationId: methodDocumentation.authSignup.operationId,
        requestBody: {
          description: 'User registration information',
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/SignupRequest'
              }
            }
          }
        },
        responses: {
          '201': {
            description: 'User created successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/SignupResponse'
                }
              }
            }
          },
          '400': {
            description: 'Invalid request data',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse'
                }
              }
            }
          }
        }
      })
    });

    // POST /auth/signin documentation
    new apigateway.CfnDocumentationPart(this, 'AuthSigninMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/auth/signin',
        method: 'POST'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.authSignin.summary,
        description: methodDocumentation.authSignin.description,
        tags: methodDocumentation.authSignin.tags,
        operationId: methodDocumentation.authSignin.operationId,
        requestBody: {
          description: 'User authentication credentials',
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/SigninRequest'
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Authentication successful',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/SigninResponse'
                }
              }
            }
          },
          '401': {
            description: 'Invalid credentials',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse'
                }
              }
            }
          }
        }
      })
    });

    // POST /auth/refresh documentation
    new apigateway.CfnDocumentationPart(this, 'AuthRefreshMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/auth/refresh',
        method: 'POST'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.authRefresh.summary,
        description: methodDocumentation.authRefresh.description,
        tags: methodDocumentation.authRefresh.tags,
        operationId: methodDocumentation.authRefresh.operationId,
        requestBody: {
          description: 'Refresh token for obtaining new access token',
          required: true,
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/RefreshRequest'
              }
            }
          }
        },
        responses: {
          '200': {
            description: 'Token refresh successful',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/RefreshResponse'
                }
              }
            }
          },
          '401': {
            description: 'Invalid or expired refresh token',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse'
                }
              }
            }
          }
        }
      })
    });

    // GET /auth/validate documentation
    new apigateway.CfnDocumentationPart(this, 'AuthValidateMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/auth/validate',
        method: 'GET'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.authValidate.summary,
        description: methodDocumentation.authValidate.description,
        tags: methodDocumentation.authValidate.tags,
        operationId: methodDocumentation.authValidate.operationId,
        parameters: [
          {
            name: 'Authorization',
            in: 'header',
            required: true,
            description: 'Bearer token for authentication',
            schema: {
              type: 'string',
              pattern: '^Bearer [A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_.+/=]*$'
            }
          }
        ],
        responses: {
          '200': {
            description: 'Token validation successful',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ValidateResponse'
                }
              }
            }
          },
          '401': {
            description: 'Invalid or expired token',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse'
                }
              }
            }
          }
        }
      })
    });

    // Posts resource documentation
    new apigateway.CfnDocumentationPart(this, 'PostsResourceDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'RESOURCE',
        path: '/posts'
      },
      properties: JSON.stringify({
        description: 'Posts management endpoints for creating, reading, updating, and deleting posts'
      })
    });

    // GET /posts documentation
    new apigateway.CfnDocumentationPart(this, 'PostsGetMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/posts',
        method: 'GET'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.postsGet.summary,
        description: methodDocumentation.postsGet.description,
        tags: methodDocumentation.postsGet.tags
      })
    });

    // POST /posts documentation
    new apigateway.CfnDocumentationPart(this, 'PostsPostMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/posts',
        method: 'POST'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.postsPost.summary,
        description: methodDocumentation.postsPost.description,
        tags: methodDocumentation.postsPost.tags
      })
    });

    // Users resource documentation
    new apigateway.CfnDocumentationPart(this, 'UsersResourceDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'RESOURCE',
        path: '/users'
      },
      properties: JSON.stringify({
        description: 'User management endpoints for profiles, following, and user interactions'
      })
    });

    // GET /users/profile documentation
    new apigateway.CfnDocumentationPart(this, 'UsersProfileGetMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/users/profile',
        method: 'GET'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.usersProfileGet.summary,
        description: methodDocumentation.usersProfileGet.description,
        tags: methodDocumentation.usersProfileGet.tags
      })
    });

    // PUT /users/profile documentation
    new apigateway.CfnDocumentationPart(this, 'UsersProfilePutMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/users/profile',
        method: 'PUT'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.usersProfilePut.summary,
        description: methodDocumentation.usersProfilePut.description,
        tags: methodDocumentation.usersProfilePut.tags
      })
    });

    // Media resource documentation
    new apigateway.CfnDocumentationPart(this, 'MediaResourceDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'RESOURCE',
        path: '/media'
      },
      properties: JSON.stringify({
        description: 'Media management endpoints for uploading, retrieving, and managing media files'
      })
    });

    // POST /media/upload documentation
    new apigateway.CfnDocumentationPart(this, 'MediaUploadPostMethodDoc', {
      restApiId: api.restApiId,
      location: {
        type: 'METHOD',
        path: '/media/upload',
        method: 'POST'
      },
      properties: JSON.stringify({
        summary: methodDocumentation.mediaUploadPost.summary,
        description: methodDocumentation.mediaUploadPost.description,
        tags: methodDocumentation.mediaUploadPost.tags
      })
    });

    // Create a documentation version to publish the documentation
    new apigateway.CfnDocumentationVersion(this, 'ApiDocumentationVersion', {
      restApiId: api.restApiId,
      documentationVersion: '1.0.0',
      description: 'GameFlex API v1.0.0 - Complete API with authentication, posts, users, and media endpoints with comprehensive validation and documentation'
    });
  }

  private createOrAttachCustomDomain(domainName: string, certificateArn: string, environment: string): void {
    // Create or reference the custom domain for API Gateway
    // For development and staging, we reference existing domains; for production, we create new ones
    let customDomain: apigateway.IDomainName;

    if (environment === 'development') {
      // Reference the existing custom domain for development
      customDomain = apigateway.DomainName.fromDomainNameAttributes(this, 'ExistingCustomDomain', {
        domainName: domainName,
        domainNameAliasHostedZoneId: 'Z2FDTNDATAQYW2', // CloudFront hosted zone ID
        domainNameAliasTarget: 'd244o0xtb4tdqw.cloudfront.net', // The actual CloudFront distribution
      });
    } else if (environment === 'staging') {
      // Reference the existing custom domain for staging
      customDomain = apigateway.DomainName.fromDomainNameAttributes(this, 'ExistingCustomDomain', {
        domainName: domainName,
        domainNameAliasHostedZoneId: 'Z2FDTNDATAQYW2', // CloudFront hosted zone ID
        domainNameAliasTarget: 'd2q10y6jcz5xpl.cloudfront.net', // The actual CloudFront distribution
      });
    } else {
      // Create new custom domain for production
      customDomain = new apigateway.DomainName(this, 'CustomDomain', {
        domainName: domainName,
        certificate: certificatemanager.Certificate.fromCertificateArn(
          this,
          'Certificate',
          certificateArn
        ),
        endpointType: apigateway.EndpointType.EDGE,
        securityPolicy: apigateway.SecurityPolicy.TLS_1_2,
      });
    }

    // Create base path mapping to connect the domain to the current API
    // This allows CDK to manage the mapping and update it if needed
    new apigateway.BasePathMapping(this, 'BasePathMapping', {
      domainName: customDomain,
      restApi: this.api,
      stage: this.api.deploymentStage,
    });

    // Output the custom domain URL
    new cdk.CfnOutput(this, 'CustomDomainUrl', {
      value: `https://${domainName}`,
      description: 'Custom domain URL for the API',
      exportName: `${this.stackName}-CustomDomainUrl`,
    });

    // Output the CloudFront distribution domain for reference
    if (environment === 'development') {
      new cdk.CfnOutput(this, 'CustomDomainTarget', {
        value: 'd244o0xtb4tdqw.cloudfront.net',
        description: 'CloudFront distribution domain name for DNS CNAME record',
        exportName: `${this.stackName}-CustomDomainTarget`,
      });

      new cdk.CfnOutput(this, 'CustomDomainHostedZoneId', {
        value: 'Z2FDTNDATAQYW2',
        description: 'CloudFront distribution hosted zone ID',
        exportName: `${this.stackName}-CustomDomainHostedZoneId`,
      });
    } else if (environment === 'staging') {
      new cdk.CfnOutput(this, 'CustomDomainTarget', {
        value: 'd2q10y6jcz5xpl.cloudfront.net',
        description: 'CloudFront distribution domain name for DNS CNAME record',
        exportName: `${this.stackName}-CustomDomainTarget`,
      });

      new cdk.CfnOutput(this, 'CustomDomainHostedZoneId', {
        value: 'Z2FDTNDATAQYW2',
        description: 'CloudFront distribution hosted zone ID',
        exportName: `${this.stackName}-CustomDomainHostedZoneId`,
      });
    } else {
      // For production, output the actual CloudFront distribution details
      new cdk.CfnOutput(this, 'CustomDomainTarget', {
        value: (customDomain as apigateway.DomainName).domainNameAliasDomainName,
        description: 'CloudFront distribution domain name for DNS CNAME record',
        exportName: `${this.stackName}-CustomDomainTarget`,
      });

      new cdk.CfnOutput(this, 'CustomDomainHostedZoneId', {
        value: (customDomain as apigateway.DomainName).domainNameAliasHostedZoneId,
        description: 'CloudFront distribution hosted zone ID',
        exportName: `${this.stackName}-CustomDomainHostedZoneId`,
      });
    }

    // Output a note about the configuration
    new cdk.CfnOutput(this, 'CustomDomainNote', {
      value: `Custom domain ${domainName} is managed by CDK and mapped to this API Gateway`,
      description: 'Custom domain configuration status',
      exportName: `${this.stackName}-CustomDomainNote`,
    });
  }

  private createOutputs(
    environment: string,
    domainName?: string,
    r2Secret?: secretsmanager.ISecret,
    appConfigSecret?: secretsmanager.ISecret,
    appleConfigSecret?: secretsmanager.ISecret
  ): void {
    // API Gateway URL
    new cdk.CfnOutput(this, 'ApiGatewayUrl', {
      value: this.api.url,
      description: 'API Gateway URL',
      exportName: `${this.stackName}-ApiGatewayUrl`,
    });

    // API Documentation URL (OpenAPI/Swagger export)
    new cdk.CfnOutput(this, 'ApiDocumentationUrl', {
      value: `https://${this.api.restApiId}.execute-api.${this.region}.amazonaws.com/v1/_doc`,
      description: 'API Documentation URL (OpenAPI/Swagger export)',
      exportName: `${this.stackName}-ApiDocumentationUrl`,
    });

    // User Pool ID
    new cdk.CfnOutput(this, 'UserPoolId', {
      value: this.userPool.userPoolId,
      description: 'Cognito User Pool ID',
      exportName: `${this.stackName}-UserPoolId`,
    });

    // User Pool Client ID
    new cdk.CfnOutput(this, 'UserPoolClientId', {
      value: this.userPoolClient.userPoolClientId,
      description: 'Cognito User Pool Client ID',
      exportName: `${this.stackName}-UserPoolClientId`,
    });

    // Custom Domain URL is now handled in attachExistingCustomDomain method

    // R2 Secret ARN
    if (r2Secret) {
      new cdk.CfnOutput(this, 'R2SecretArn', {
        value: r2Secret.secretArn,
        description: 'R2 Configuration Secret ARN',
        exportName: `${this.stackName}-R2SecretArn`,
      });
    }

    // App Config Secret ARN
    if (appConfigSecret) {
      new cdk.CfnOutput(this, 'AppConfigSecretArn', {
        value: appConfigSecret.secretArn,
        description: 'App Configuration Secret ARN',
        exportName: `${this.stackName}-AppConfigSecretArn`,
      });
    }

    // Apple Config Secret ARN
    if (appleConfigSecret) {
      new cdk.CfnOutput(this, 'AppleConfigSecretArn', {
        value: appleConfigSecret.secretArn,
        description: 'Apple Sign In Configuration Secret ARN',
        exportName: `${this.stackName}-AppleConfigSecretArn`,
      });
    }

    // DynamoDB Table Names
    Object.entries(this.tables).forEach(([name, table]) => {
      new cdk.CfnOutput(this, `${name.charAt(0).toUpperCase() + name.slice(1)}TableName`, {
        value: table.tableName,
        description: `${name} DynamoDB Table Name`,
        exportName: `${this.stackName}-${name.charAt(0).toUpperCase() + name.slice(1)}TableName`,
      });
    });
  }
}
