import 'dart:developer' as developer;
import '../models/post_model.dart';
import '../utils/app_logger.dart';
import 'api_service.dart';

/// AWS posts service for managing posts via API Gateway
class AwsPostsService {
  static AwsPostsService? _instance;
  static AwsPostsService get instance => _instance ??= AwsPostsService._();

  AwsPostsService._();

  /// Get posts with pagination
  Future<List<PostModel>> getPosts({
    int limit = 20,
    int offset = 0,
    String? channelId,
  }) async {
    AppLogger.posts('getPosts: Starting with limit=$limit, offset=$offset');
    try {
      AppLogger.posts('getPosts: Inside try block');
      developer.log(
        'AwsPostsService: Getting posts (limit: $limit, offset: $offset)',
      );

      AppLogger.posts('getPosts: Building query params');
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (channelId != null) {
        queryParams['channelId'] = channelId;
      }

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final path = '/posts${queryString.isNotEmpty ? '?$queryString' : ''}';
      AppLogger.posts('getPosts: Making API request to path: $path');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: path,
      );
      AppLogger.posts('getPosts: Received API response');

      AppLogger.posts('getPosts: Parsing response');
      final data = ApiService.instance.parseResponse(response);
      AppLogger.posts('Parsed response data keys: ${data.keys}');

      final postsData = data['posts'] as List<dynamic>? ?? [];
      AppLogger.posts('Posts data type: ${postsData.runtimeType}');
      AppLogger.posts('Posts data length: ${postsData.length}');

      AppLogger.posts('Processing ${postsData.length} posts from API');

      final posts = <PostModel>[];
      for (int i = 0; i < postsData.length; i++) {
        AppLogger.posts('Processing post $i');
        try {
          final postData = postsData[i] as Map<String, dynamic>;
          AppLogger.posts('Post $i ID: ${postData['id']}');
          AppLogger.posts('Post $i media_url: ${postData['media_url']}');

          AppLogger.posts('Converting post $i to PostModel');
          final post = _convertToPostModel(postData);
          AppLogger.posts('Successfully converted post $i');
          posts.add(post);
          AppLogger.posts('Added post $i to list');
        } catch (e, stackTrace) {
          AppLogger.error(
            'Error processing post $i',
            error: e,
            stackTrace: stackTrace,
          );
          AppLogger.error('Post data: ${postsData[i]}');
          // Continue processing other posts instead of failing completely
        }
      }

      AppLogger.posts('Successfully processed all posts');
      AppLogger.posts('Returning ${posts.length} posts');
      developer.log('AwsPostsService: Retrieved ${posts.length} posts');
      return posts;
    } catch (e, stackTrace) {
      AppLogger.error(
        'MAIN CATCH BLOCK - Error getting posts',
        error: e,
        stackTrace: stackTrace,
      );
      developer.log('AwsPostsService: Error getting posts: $e');
      throw Exception('Failed to get posts: $e');
    }
  }

  /// Get a specific post by ID
  Future<PostModel?> getPost(String postId) async {
    try {
      developer.log('AwsPostsService: Getting post $postId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'GET',
        path: '/posts/$postId',
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Retrieved post $postId');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error getting post $postId: $e');
      AppLogger.error('Error getting post $postId', error: e);
      return null;
    }
  }

  /// Create a new post
  Future<PostModel?> createPost({
    required String content,
    String? channelId,
    String? mediaId,
  }) async {
    try {
      developer.log('AwsPostsService: Creating post');

      final body = <String, dynamic>{'content': content};

      if (channelId != null) {
        body['channelId'] = channelId;
      }

      if (mediaId != null) {
        body['mediaId'] = mediaId;
      }

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/posts',
        body: body,
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Created post ${post.id}');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error creating post: $e');
      AppLogger.error('Error creating post', error: e);
      throw Exception('Failed to create post: $e');
    }
  }

  /// Create a draft post (first step of multi-step creation)
  Future<PostModel?> createDraftPost({
    required String content,
    String? title,
  }) async {
    try {
      developer.log('AwsPostsService: Creating draft post');

      final body = <String, dynamic>{'content': content};

      if (title != null) {
        body['title'] = title;
      }

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/posts/draft',
        body: body,
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Created draft post ${post.id}');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error creating draft post: $e');
      AppLogger.error('Error creating draft post', error: e);
      throw Exception('Failed to create draft post: $e');
    }
  }

  /// Attach media to a draft post
  Future<PostModel?> attachMediaToPost({
    required String postId,
    required String mediaId,
  }) async {
    try {
      developer.log('AwsPostsService: Attaching media to post $postId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'PUT',
        path: '/posts/$postId/media',
        body: {'media_id': mediaId},
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Media attached to post ${post.id}');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error attaching media to post: $e');
      AppLogger.error('Error attaching media to post', error: e);
      throw Exception('Failed to attach media to post: $e');
    }
  }

  /// Publish a draft post (final step)
  Future<PostModel?> publishPost(String postId) async {
    try {
      developer.log('AwsPostsService: Publishing post $postId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'PUT',
        path: '/posts/$postId/publish',
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Published post ${post.id}');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error publishing post: $e');
      AppLogger.error('Error publishing post', error: e);
      throw Exception('Failed to publish post: $e');
    }
  }

  /// Like a post
  Future<bool> likePost(String postId) async {
    try {
      developer.log('AwsPostsService: Liking post $postId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/posts/$postId/like',
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Liked post $postId');
      return true;
    } catch (e) {
      developer.log('AwsPostsService: Error liking post $postId: $e');
      AppLogger.error('Error liking post $postId', error: e);
      return false;
    }
  }

  /// Unlike a post
  Future<bool> unlikePost(String postId) async {
    try {
      developer.log('AwsPostsService: Unliking post $postId');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'DELETE',
        path: '/posts/$postId/like',
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Unliked post $postId');
      return true;
    } catch (e) {
      developer.log('AwsPostsService: Error unliking post $postId: $e');
      AppLogger.error('Error unliking post $postId', error: e);
      return false;
    }
  }

  /// React to a post with an emoji
  Future<bool> reactToPost(String postId, String emoji) async {
    try {
      developer.log('AwsPostsService: Reacting to post $postId with $emoji');

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'POST',
        path: '/posts/$postId/reactions',
        body: {'emoji': emoji},
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Reacted to post $postId');
      return true;
    } catch (e) {
      developer.log('AwsPostsService: Error reacting to post $postId: $e');
      AppLogger.error('Error reacting to post $postId', error: e);
      return false;
    }
  }

  /// Remove a specific emoji reaction from a post by current user
  Future<bool> unreactToPost(String postId, String emoji) async {
    try {
      developer.log(
        'AwsPostsService: Removing reaction $emoji from post $postId',
      );

      final response = await ApiService.instance.makeAuthenticatedRequest(
        method: 'DELETE',
        path: '/posts/$postId/reactions',
        queryParams: {'emoji': emoji},
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Removed reaction from post $postId');
      return true;
    } catch (e) {
      developer.log(
        'AwsPostsService: Error removing reaction from post $postId: $e',
      );
      AppLogger.error('Error removing reaction from post $postId', error: e);
      return false;
    }
  }

  /// Convert AWS API response to PostModel
  PostModel _convertToPostModel(Map<String, dynamic> data) {
    try {
      AppLogger.posts('_convertToPostModel: Converting post ${data['id']}');
      AppLogger.posts('_convertToPostModel: Raw data: $data');

      // Use PostModel.fromJson for consistent parsing
      final post = PostModel.fromJson(data);
      AppLogger.posts(
        '_convertToPostModel: Created post with mediaUrl: ${post.mediaUrl}',
      );
      return post;
    } catch (e, stackTrace) {
      AppLogger.error(
        '_convertToPostModel: Error converting post',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('_convertToPostModel: Data: $data');
      rethrow;
    }
  }
}
