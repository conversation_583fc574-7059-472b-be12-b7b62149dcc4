import '../services/config_service.dart';
import '../utils/app_logger.dart';

class PostModel {
  final String id;
  final String userId;
  final String? channelId;
  final String content;
  final String? mediaUrl;
  final String? mediaType;
  final String? mediaId;
  final Map<String, dynamic>? mediaData;
  final String? s3Bucket;
  final String? s3Key;
  final int likeCount;
  final int commentCount;
  final int reflexCount;
  final bool isActive;
  final String status; // draft, uploading_media, published
  final DateTime createdAt;
  final DateTime updatedAt;

  // User information (from join)
  final String? username;
  final String? displayName;
  final String? avatarUrl;

  // Like status for current user
  final bool isLikedByCurrentUser;

  // Emoji reactions summary and current user's single reaction
  final Map<String, int> reactions;
  final String? currentUserReaction;

  PostModel({
    required this.id,
    required this.userId,
    this.channelId,
    required this.content,
    this.mediaUrl,
    this.mediaType,
    this.mediaId,
    this.mediaData,
    this.s3Bucket,
    this.s3Key,
    required this.likeCount,
    required this.commentCount,
    required this.reflexCount,
    required this.isActive,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.username,
    this.displayName,
    this.avatarUrl,
    this.isLikedByCurrentUser = false,
    Map<String, int>? reactions,
    this.currentUserReaction,
  }) : reactions = reactions ?? const {};

  factory PostModel.fromJson(Map<String, dynamic> json) {
    AppLogger.posts('fromJson: Starting to parse post ${json['id']}');
    try {
      AppLogger.posts('fromJson: Inside try block for post ${json['id']}');
      AppLogger.posts('fromJson: Raw media_url: ${json['media_url']}');
      AppLogger.posts('fromJson: Raw media_type: ${json['media_type']}');
      AppLogger.posts('fromJson: Raw media_id: ${json['media_id']}');
      AppLogger.posts('fromJson: Raw s3_bucket: ${json['s3_bucket']}');
      AppLogger.posts('fromJson: Raw s3_key: ${json['s3_key']}');
      AppLogger.posts('fromJson: Raw media: ${json['media']}');

      // Handle both old and new media formats
      AppLogger.posts('fromJson: Parsing media information');
      String? mediaUrl;
      String? mediaType;
      final mediaId = json['mediaId'] as String? ?? json['media_id'] as String?;
      final mediaData = json['media'] as Map<String, dynamic>?;

      // Handle media URL and type - prioritize the new structure
      if (mediaData != null) {
        AppLogger.posts('fromJson: Found media object');
        // For AWS backend, media URLs are already complete
        mediaUrl = mediaData['url'] as String?;
        mediaType = mediaData['mediaType'] as String?;
        AppLogger.posts(
          'fromJson: Using media object - URL: $mediaUrl, Type: $mediaType',
        );
      } else if (json['mediaUrl'] != null || json['media_url'] != null) {
        AppLogger.posts('fromJson: Using direct mediaUrl field');
        // Use the mediaUrl field directly (new backend structure) or fallback to media_url
        mediaUrl = json['mediaUrl'] as String? ?? json['media_url'] as String?;
        mediaType =
            json['mediaType'] as String? ?? json['media_type'] as String?;
        AppLogger.posts(
          'fromJson: Using direct fields - URL: $mediaUrl, Type: $mediaType',
        );
      } else if (json['s3Bucket'] != null && json['s3Key'] != null ||
          json['s3_bucket'] != null && json['s3_key'] != null) {
        AppLogger.posts(
          'fromJson: Found S3 bucket and key, will construct URL later',
        );
        // Store S3 info for later URL construction
        final s3Bucket =
            json['s3Bucket'] as String? ?? json['s3_bucket'] as String?;
        final s3Key = json['s3Key'] as String? ?? json['s3_key'] as String?;
        // Set mediaUrl to null for now, will be constructed when needed
        mediaUrl = null;
        mediaType =
            json['mediaType'] as String? ?? json['media_type'] as String?;
        AppLogger.posts(
          'fromJson: Stored S3 info - Bucket: $s3Bucket, Key: $s3Key, Type: $mediaType',
        );
      } else {
        AppLogger.posts('fromJson: No media information found');
        mediaUrl = null;
        mediaType = null;
      }

      AppLogger.posts('fromJson: Creating PostModel with mediaUrl: $mediaUrl');
      // Handle both userId/user_id and authorId/author_id field names for backward compatibility
      final userId =
          json['userId'] as String? ??
          json['user_id'] as String? ??
          json['authorId'] as String? ??
          json['author_id'] as String?;
      if (userId == null) {
        throw Exception(
          'Missing userId, user_id, authorId, or author_id field in post JSON',
        );
      }

      // Handle both API field formats: 'likes'/'likeCount'/'like_count', 'comments'/'commentCount'/'comment_count', and 'reflexes'/'reflexCount'/'reflex_count'
      final likeCount =
          (json['likeCount'] as int?) ??
          (json['like_count'] as int?) ??
          (json['likes'] as int?) ??
          0;
      final commentCount =
          (json['commentCount'] as int?) ??
          (json['comment_count'] as int?) ??
          (json['comments'] as int?) ??
          0;
      final reflexCount =
          (json['reflexCount'] as int?) ??
          (json['reflex_count'] as int?) ??
          (json['reflexes'] as int?) ??
          0;
      final isLikedByCurrentUser =
          json['isLikedByCurrentUser'] as bool? ??
          json['is_liked_by_current_user'] as bool? ??
          false;

      final postModel = PostModel(
        id: json['id'] as String,
        userId: userId,
        channelId:
            json['channelId'] as String? ?? json['channel_id'] as String?,
        content: json['content'] as String? ?? '',
        mediaUrl: mediaUrl,
        mediaType: mediaType,
        mediaId: mediaId,
        mediaData: mediaData,
        s3Bucket: json['s3Bucket'] as String? ?? json['s3_bucket'] as String?,
        s3Key: json['s3Key'] as String? ?? json['s3_key'] as String?,
        likeCount: likeCount,
        commentCount: commentCount,
        reflexCount: reflexCount,
        isActive:
            json['active'] as bool? ??
            json['isActive'] as bool? ??
            json['is_active'] as bool? ??
            true,
        status: json['status'] as String? ?? 'published',
        createdAt: DateTime.parse(
          json['createdAt'] as String? ?? json['created_at'] as String,
        ),
        updatedAt: DateTime.parse(
          json['updatedAt'] as String? ?? json['updated_at'] as String,
        ),
        username: json['username'] as String?,
        displayName:
            json['displayName'] as String? ?? json['display_name'] as String?,
        avatarUrl:
            json['avatarUrl'] as String? ?? json['avatar_url'] as String?,
        isLikedByCurrentUser: isLikedByCurrentUser,
        reactions:
            (json['reactions'] as Map?)?.map(
              (k, v) => MapEntry('$k', (v as num).toInt()),
            ) ??
            const {},
        currentUserReaction:
            (json['currentUserReactions'] as List?)?.isNotEmpty == true
                ? (json['currentUserReactions'] as List).first.toString()
                : null,
      );
      AppLogger.posts(
        'fromJson: Successfully created PostModel with mediaUrl: ${postModel.mediaUrl}',
      );
      return postModel;
    } catch (e, stackTrace) {
      AppLogger.error(
        'PostModel.fromJson: Error parsing post',
        error: e,
        stackTrace: stackTrace,
      );
      AppLogger.error('PostModel.fromJson: JSON: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'channelId': channelId,
      'content': content,
      'mediaUrl': mediaUrl,
      'mediaType': mediaType,
      'mediaId': mediaId,
      's3Bucket': s3Bucket,
      's3Key': s3Key,
      'likeCount': likeCount,
      'commentCount': commentCount,
      'reflexCount': reflexCount,
      'isActive': isActive,
      'active': isActive,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'username': username,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'isLikedByCurrentUser': isLikedByCurrentUser,
      'reactions': reactions,
      'currentUserReaction': currentUserReaction,
    };
  }

  PostModel copyWith({
    String? id,
    String? userId,
    String? channelId,
    String? content,
    String? mediaUrl,
    String? mediaType,
    String? mediaId,
    Map<String, dynamic>? mediaData,
    String? s3Bucket,
    String? s3Key,
    int? likeCount,
    int? commentCount,
    int? reflexCount,
    bool? isActive,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? username,
    String? displayName,
    String? avatarUrl,
    bool? isLikedByCurrentUser,
    Map<String, int>? reactions,
    String? currentUserReaction,
  }) {
    return PostModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      channelId: channelId ?? this.channelId,
      content: content ?? this.content,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      mediaType: mediaType ?? this.mediaType,
      mediaId: mediaId ?? this.mediaId,
      mediaData: mediaData ?? this.mediaData,
      s3Bucket: s3Bucket ?? this.s3Bucket,
      s3Key: s3Key ?? this.s3Key,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      reflexCount: reflexCount ?? this.reflexCount,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
      reactions: reactions ?? this.reactions,
      currentUserReaction: currentUserReaction ?? this.currentUserReaction,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 7) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get authorDisplayName {
    if (displayName != null && displayName!.isNotEmpty) {
      return displayName!;
    }
    if (username != null && username!.isNotEmpty) {
      return username!;
    }
    return 'User';
  }

  String get authorUsername {
    if (username != null && username!.isNotEmpty) {
      return '@${username!}';
    }
    return '@user';
  }

  bool get hasMedia => mediaUrl != null && mediaUrl!.isNotEmpty;

  bool get isImage => mediaType == 'image';

  bool get isVideo => mediaType == 'video';

  @override
  String toString() {
    return 'PostModel(id: $id, content: $content, author: $authorDisplayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PostModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Get the media URL directly
  Future<String?> getMediaUrl() async {
    // Just return the media URL directly
    return mediaUrl;
  }

  /// Helper method to construct S3 media URL using configuration service
  static Future<String> constructS3MediaUrl(String bucket, String key) async {
    final s3BaseUrl = await ConfigService.instance.getS3Url();
    return '$s3BaseUrl/$bucket/$key';
  }
}
